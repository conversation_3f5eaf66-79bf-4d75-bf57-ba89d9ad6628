/**
 * Améliorations du compteur Golden Sky Home
 * Styles pour une meilleure visibilité et impact visuel
 */

/* Animation de pulsation pour les chiffres */
@keyframes countdownPulse {
    0% { 
        transform: scale(1);
        text-shadow: 0 0 30px rgba(156, 119, 25, 0.8), 0 0 60px rgba(156, 119, 25, 0.4);
    }
    50% { 
        transform: scale(1.02);
        text-shadow: 0 0 40px rgba(156, 119, 25, 1), 0 0 80px rgba(156, 119, 25, 0.6);
    }
    100% { 
        transform: scale(1);
        text-shadow: 0 0 30px rgba(156, 119, 25, 0.8), 0 0 60px rgba(156, 119, 25, 0.4);
    }
}

/* Animation de brillance pour le texte */
@keyframes textGlow {
    0% { 
        text-shadow: 0 0 20px rgba(156, 119, 25, 0.6);
    }
    50% { 
        text-shadow: 0 0 30px rgba(156, 119, 25, 0.9), 0 0 50px rgba(156, 119, 25, 0.5);
    }
    100% { 
        text-shadow: 0 0 20px rgba(156, 119, 25, 0.6);
    }
}

/* Amélioration du compteur principal */
.golden-sky-theme .pane-when .clock .elem-center .digit {
    animation: countdownPulse 3s ease-in-out infinite;
    position: relative;
}

.golden-sky-theme .pane-when .clock .elem-center .digit::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(156, 119, 25, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.golden-sky-theme .pane-when .clock .elem-center .txt {
    animation: textGlow 2s ease-in-out infinite;
}

/* Amélioration du texte du bas */
.golden-sky-theme .pane-when .clock .elem-bottom span {
    display: inline-block;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.golden-sky-theme .pane-when .clock .elem-bottom span:hover {
    transform: scale(1.1);
    text-shadow: 0 0 25px rgba(156, 119, 25, 0.9);
}

/* Amélioration du footer */
.golden-sky-theme .pane-when footer {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
    margin-top: 40px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(156, 119, 25, 0.3);
}

.golden-sky-theme .pane-when footer p {
    margin: 0 0 10px 0;
    line-height: 1.5;
}

.golden-sky-theme .pane-when footer .opening-date {
    background: linear-gradient(135deg, rgba(156, 119, 25, 0.2) 0%, rgba(156, 119, 25, 0.1) 100%);
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid rgba(156, 119, 25, 0.4);
    display: inline-block;
    margin-top: 10px;
}

/* Icône du timer avec animation */
.golden-sky-theme .timer-icon {
    animation: textGlow 2.5s ease-in-out infinite;
    display: inline-block;
    margin-right: 8px;
}

/* Responsive amélioré */
@media (max-width: 1024px) {
    .golden-sky-theme .pane-when footer {
        padding: 15px;
        margin-top: 30px;
    }
    
    .golden-sky-theme .pane-when footer p {
        font-size: 1rem;
    }
    
    .golden-sky-theme .pane-when footer .opening-date {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .golden-sky-theme .pane-when .clock .elem-center .digit {
        animation-duration: 4s; /* Plus lent sur mobile */
    }
    
    .golden-sky-theme .pane-when footer {
        padding: 12px;
        margin-top: 25px;
    }
    
    .golden-sky-theme .pane-when footer p {
        font-size: 0.95rem;
    }
    
    .golden-sky-theme .pane-when footer .opening-date {
        font-size: 1rem;
        padding: 8px 12px;
    }
}

@media (max-width: 600px) {
    .golden-sky-theme .pane-when .clock .elem-bottom {
        font-size: 18px;
    }
    
    .golden-sky-theme .pane-when footer {
        padding: 10px;
        margin-top: 20px;
    }
    
    .golden-sky-theme .pane-when footer p {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }
    
    .golden-sky-theme .pane-when footer .opening-date {
        font-size: 0.95rem;
        padding: 6px 10px;
    }
}

/* Effet de survol pour l'ensemble du compteur */
.golden-sky-theme .pane-when .clock:hover .elem-center .digit {
    animation-duration: 1.5s;
    transform: scale(1.05);
}

.golden-sky-theme .pane-when .clock:hover .elem-center .txt {
    animation-duration: 1s;
}

/* Amélioration de la lisibilité avec un fond subtil */
.golden-sky-theme .pane-when .clock {
    position: relative;
}

.golden-sky-theme .pane-when .clock::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(0, 0, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

/* Animation d'apparition au chargement */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.golden-sky-theme .pane-when .clock {
    animation: fadeInUp 1s ease-out;
}

.golden-sky-theme .pane-when footer {
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Amélioration des séparateurs */
.golden-sky-theme .pane-when .clock .elem-bottom .thin {
    position: relative;
    margin: 0 4px;
}

.golden-sky-theme .pane-when .clock .elem-bottom .thin::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(156, 119, 25, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

/* Effet de particules dorées (optionnel) */
.golden-sky-theme .pane-when::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(156, 119, 25, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(156, 119, 25, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(156, 119, 25, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(156, 119, 25, 0.3), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkle 10s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes sparkle {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}
