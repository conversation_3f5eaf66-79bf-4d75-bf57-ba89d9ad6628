/**
 * Script de gestion des emails pour Golden Sky Home
 * Gère les formulaires de réservation/newsletter et de contact
 */

$(document).ready(function() {
    
    // Configuration des messages
    const messages = {
        fr: {
            sending: 'Envoi en cours...',
            emailSuccess: 'Merci pour votre inscription ! Nous vous tiendrons informé.',
            messageSuccess: 'Votre message a été envoyé avec succès ! Nous vous répondrons bientôt.',
            error: 'Erreur lors de l\'envoi. Veuillez réessayer.',
            networkError: 'Erreur de connexion. Vérifiez votre connexion internet.',
            invalidEmail: 'Veuillez saisir une adresse email valide.',
            requiredFields: 'Veuillez remplir tous les champs requis.'
        },
        en: {
            sending: 'Sending...',
            emailSuccess: 'Thank you for subscribing! We will keep you informed.',
            messageSuccess: 'Your message has been sent successfully! We will reply soon.',
            error: 'Error sending. Please try again.',
            networkError: 'Connection error. Check your internet connection.',
            invalidEmail: 'Please enter a valid email address.',
            requiredFields: 'Please fill in all required fields.'
        }
    };

    // Détecter la langue actuelle
    const currentLang = $('html').attr('lang') || 'fr';
    const msg = messages[currentLang] || messages.fr;

    // Fonction pour afficher les messages
    function showMessage(element, message, type = 'success') {
        const messageElement = element.find('.email-ok, .message-ok');
        messageElement.removeClass('invisible success error')
                     .addClass(type)
                     .html(message)
                     .fadeIn();
        
        // Masquer après 5 secondes si succès
        if (type === 'success') {
            setTimeout(() => {
                messageElement.fadeOut();
            }, 5000);
        }
    }

    // Fonction pour valider l'email
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // Fonction pour envoyer les données
    function sendFormData(form, successMessage) {
        const formData = new FormData(form[0]);
        formData.append('ajax', '1');
        
        const submitButton = form.find('button[type="submit"]');
        const originalText = submitButton.text();
        
        // Désactiver le bouton et changer le texte
        submitButton.prop('disabled', true).text(msg.sending);
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 30000,
            success: function(response) {
                if (response.success) {
                    showMessage(form, successMessage, 'success');
                    form[0].reset(); // Réinitialiser le formulaire
                } else {
                    showMessage(form, response.message || msg.error, 'error');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = msg.networkError;
                if (status === 'timeout') {
                    errorMessage = 'Délai d\'attente dépassé. Veuillez réessayer.';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showMessage(form, errorMessage, 'error');
            },
            complete: function() {
                // Réactiver le bouton
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    }

    // Gestion du formulaire de newsletter/réservation
    $('.send_email_form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const email = form.find('input[name="email"]').val().trim();
        
        // Validation
        if (!email) {
            showMessage(form, msg.requiredFields, 'error');
            return;
        }
        
        if (!validateEmail(email)) {
            showMessage(form, msg.invalidEmail, 'error');
            return;
        }
        
        sendFormData(form, msg.emailSuccess);
    });

    // Gestion du formulaire de contact/message
    $('.send_message_form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const name = form.find('input[name="name"]').val().trim();
        const email = form.find('input[name="email"]').val().trim();
        const message = form.find('textarea[name="message"]').val().trim();
        
        // Validation
        if (!name || !email || !message) {
            showMessage(form, msg.requiredFields, 'error');
            return;
        }
        
        if (!validateEmail(email)) {
            showMessage(form, msg.invalidEmail, 'error');
            return;
        }
        
        if (message.length < 10) {
            showMessage(form, 'Le message doit contenir au moins 10 caractères.', 'error');
            return;
        }
        
        sendFormData(form, msg.messageSuccess);
    });

    // Amélioration de l'UX : validation en temps réel
    $('input[type="email"]').on('blur', function() {
        const email = $(this).val().trim();
        if (email && !validateEmail(email)) {
            $(this).addClass('error-field');
        } else {
            $(this).removeClass('error-field');
        }
    });

    // Styles CSS pour les erreurs
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .error-field {
                border-color: #dc3545 !important;
                box-shadow: 0 0 5px rgba(220, 53, 69, 0.3) !important;
            }
            .email-ok.success, .message-ok.success {
                background: rgba(40, 167, 69, 0.2);
                border: 1px solid #28a745;
                color: #28a745;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
            }
            .email-ok.error, .message-ok.error {
                background: rgba(220, 53, 69, 0.2);
                border: 1px solid #dc3545;
                color: #dc3545;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
            }
            .golden-sky-theme .email-ok.success,
            .golden-sky-theme .message-ok.success {
                background: rgba(156, 119, 25, 0.2);
                border: 1px solid #9c7719;
                color: #9c7719;
            }
        `)
        .appendTo('head');

    // Animation d'envoi
    function addSendingAnimation(button) {
        let dots = 0;
        const interval = setInterval(() => {
            dots = (dots + 1) % 4;
            const dotString = '.'.repeat(dots);
            button.text(msg.sending + dotString);
        }, 500);
        
        button.data('animation-interval', interval);
    }

    function removeSendingAnimation(button, originalText) {
        const interval = button.data('animation-interval');
        if (interval) {
            clearInterval(interval);
            button.removeData('animation-interval');
        }
        button.text(originalText);
    }

    // Améliorer l'animation des boutons
    $('.send_email_form, .send_message_form').on('submit', function() {
        const button = $(this).find('button[type="submit"]');
        const originalText = button.text();
        addSendingAnimation(button);
        
        // Nettoyer l'animation après 30 secondes max
        setTimeout(() => {
            removeSendingAnimation(button, originalText);
        }, 30000);
    });

    console.log('Golden Sky Home - Système de mail activé ✅');
});

// Fonction globale pour tester l'envoi d'email (debug)
window.testGoldenSkyMail = function() {
    console.log('Test du système de mail Golden Sky Home');
    
    // Simuler un envoi de newsletter
    const testData = {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Newsletter',
        message: 'Ceci est un test du système de mail.'
    };
    
    $.post('sendmail.php', testData)
        .done(function(response) {
            console.log('✅ Test réussi:', response);
        })
        .fail(function(error) {
            console.log('❌ Test échoué:', error);
        });
};
