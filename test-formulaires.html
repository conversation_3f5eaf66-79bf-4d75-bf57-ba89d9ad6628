<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Formulaires - Golden Sky Home</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
            color: #f8f8f8;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(26, 26, 26, 0.9);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #9c7719;
        }
        h1, h2 {
            color: #9c7719;
            text-align: center;
        }
        .form-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #9c7719;
            border-radius: 10px;
            background: rgba(156, 119, 25, 0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #9c7719;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #9c7719;
            border-radius: 5px;
            background: rgba(26, 26, 26, 0.8);
            color: #f8f8f8;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #9c7719 0%, #d4af37 100%);
            color: #1a1a1a;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(156, 119, 25, 0.4);
        }
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        .error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        .info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Formulaires Golden Sky Home</h1>
        
        <div class="info">
            <h3>📧 Configuration Email :</h3>
            <p><strong>Destinataire :</strong> <EMAIL></p>
            <p><strong>Script :</strong> sendmail.php</p>
            <p><strong>Méthode :</strong> POST avec AJAX</p>
        </div>

        <!-- Test Formulaire Newsletter -->
        <div class="form-section">
            <h2>📬 Test Formulaire Newsletter/Réservation</h2>
            <p>Simule le formulaire de la section "Réservation" de votre site.</p>
            
            <form id="newsletter-test" class="send_email_form" method="POST" action="sendmail.php">
                <div class="form-group">
                    <label for="newsletter-email">Email :</label>
                    <input type="email" id="newsletter-email" name="email" required placeholder="<EMAIL>">
                    <input type="hidden" name="name" value="Abonné Newsletter">
                    <input type="hidden" name="subject" value="Inscription Newsletter - Golden Sky Home">
                    <input type="hidden" name="message" value="Nouvelle inscription à la newsletter pour recevoir les dernières nouvelles de Golden Sky Home.">
                </div>
                <button type="submit">S'inscrire à la Newsletter</button>
                <div class="success email-ok invisible"></div>
            </form>
        </div>

        <!-- Test Formulaire Contact -->
        <div class="form-section">
            <h2>💬 Test Formulaire Contact/Message</h2>
            <p>Simule le formulaire de contact de votre site.</p>
            
            <form id="contact-test" class="send_message_form" method="POST" action="sendmail.php">
                <div class="form-group">
                    <label for="contact-name">Nom :</label>
                    <input type="text" id="contact-name" name="name" required placeholder="Votre nom">
                </div>
                <div class="form-group">
                    <label for="contact-email">Email :</label>
                    <input type="email" id="contact-email" name="email" required placeholder="<EMAIL>">
                    <input type="hidden" name="subject" value="Message depuis le site Golden Sky Home">
                </div>
                <div class="form-group">
                    <label for="contact-message">Message :</label>
                    <textarea id="contact-message" name="message" required placeholder="Votre message..." rows="4"></textarea>
                </div>
                <button type="submit">Envoyer le Message</button>
                <div class="success message-ok invisible"></div>
            </form>
        </div>

        <!-- Test Formulaire Complet -->
        <div class="form-section">
            <h2>🎯 Test Formulaire Complet</h2>
            <p>Test avec tous les champs disponibles.</p>
            
            <form id="complete-test" method="POST" action="sendmail.php">
                <div class="form-group">
                    <label for="complete-name">Nom complet :</label>
                    <input type="text" id="complete-name" name="name" required placeholder="Nom complet">
                </div>
                <div class="form-group">
                    <label for="complete-email">Email :</label>
                    <input type="email" id="complete-email" name="email" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="complete-phone">Téléphone :</label>
                    <input type="tel" id="complete-phone" name="phone" placeholder="+212 XXX-XXXXXX">
                </div>
                <div class="form-group">
                    <label for="complete-subject">Sujet :</label>
                    <select id="complete-subject" name="subject">
                        <option value="Demande de réservation">Demande de réservation</option>
                        <option value="Information générale">Information générale</option>
                        <option value="Événement spécial">Événement spécial</option>
                        <option value="Partenariat">Partenariat</option>
                        <option value="Autre">Autre</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="complete-message">Message :</label>
                    <textarea id="complete-message" name="message" required placeholder="Décrivez votre demande..." rows="5"></textarea>
                </div>
                <button type="submit">Envoyer (Test Complet)</button>
                <div class="success" id="complete-success"></div>
                <div class="error" id="complete-error"></div>
            </form>
        </div>

        <div class="info">
            <h3>🔍 Instructions de test :</h3>
            <ol>
                <li>Testez chaque formulaire individuellement</li>
                <li>Vérifiez la réception des emails dans <code><EMAIL></code></li>
                <li>Observez les messages de succès/erreur</li>
                <li>Testez avec des emails invalides pour voir la validation</li>
                <li>Vérifiez que les boutons se désactivent pendant l'envoi</li>
            </ol>
        </div>

        <div class="info">
            <h3>⚠️ Après les tests :</h3>
            <p><strong>Supprimez ce fichier de test</strong> de votre serveur de production !</p>
        </div>
    </div>

    <!-- Scripts nécessaires -->
    <script src="js/vendor/jquery-1.11.2.min.js"></script>
    <script src="js/golden-sky-mail.js"></script>
    
    <!-- Script pour le formulaire complet -->
    <script>
        $(document).ready(function() {
            $('#complete-test').on('submit', function(e) {
                e.preventDefault();
                
                const form = $(this);
                const formData = new FormData(this);
                formData.append('ajax', '1');
                
                const button = form.find('button');
                const originalText = button.text();
                
                button.prop('disabled', true).text('Envoi en cours...');
                $('#complete-success, #complete-error').hide();
                
                $.ajax({
                    url: 'sendmail.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#complete-success').text(response.message).show();
                            form[0].reset();
                        } else {
                            $('#complete-error').text(response.message).show();
                        }
                    },
                    error: function() {
                        $('#complete-error').text('Erreur de connexion').show();
                    },
                    complete: function() {
                        button.prop('disabled', false).text(originalText);
                    }
                });
            });
        });
    </script>
</body>
</html>
