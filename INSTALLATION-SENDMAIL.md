# Installation du système d'envoi d'emails - Golden Sky Home

## 📁 Structure des fichiers requise

```
votre-site/
├── sendmail.php                    # Script principal d'envoi
├── contact-form-example.html       # Exemple de formulaire
├── phpmailer/                      # Dossier PHPMailer
│   ├── PHPMailer.php
│   ├── SMTP.php
│   └── Exception.php
└── INSTALLATION-SENDMAIL.md        # Ce fichier
```

## 🚀 Étapes d'installation

### 1. Télécharger PHPMailer

**Option A : Téléchargement direct**
- Allez sur : https://github.com/PHPMailer/PHPMailer/releases
- Téléchargez la dernière version (ZIP)
- Extrayez les fichiers `PHPMailer.php`, `SMTP.php`, `Exception.php`
- Placez-les dans le dossier `phpmailer/`

**Option B : Via Composer (si disponible)**
```bash
composer require phpmailer/phpmailer
```

### 2. Configuration du mot de passe

Dans le fichier `sendmail.php`, ligne 25 :
```php
define('SMTP_PASSWORD', 'VOTRE_MOT_DE_PASSE_ICI');
```

**Remplacez par votre vrai mot de passe email !**

### 3. Upload sur Namecheap

1. Connectez-vous à votre cPanel Namecheap
2. Ouvrez le gestionnaire de fichiers
3. Naviguez vers `public_html/`
4. Uploadez tous les fichiers en respectant la structure

### 4. Permissions des fichiers

Assurez-vous que les permissions sont correctes :
- `sendmail.php` : 644
- Dossier `phpmailer/` : 755
- Fichiers dans `phpmailer/` : 644

## ⚙️ Configuration email Namecheap

### Paramètres SMTP utilisés :
- **Host** : goldenskyhome.ma
- **Port** : 465
- **Sécurité** : SSL
- **Authentification** : Oui
- **Username** : <EMAIL>

### Créer l'adresse email

1. Dans cPanel → **Email Accounts**
2. Créez l'adresse : `<EMAIL>`
3. Définissez un mot de passe fort
4. Notez le mot de passe pour le script

## 🔧 Intégration dans votre site

### Formulaire simple
```html
<form action="sendmail.php" method="POST">
    <input type="text" name="name" placeholder="Nom" required>
    <input type="email" name="email" placeholder="Email" required>
    <textarea name="message" placeholder="Message" required></textarea>
    <button type="submit">Envoyer</button>
</form>
```

### Formulaire AJAX (recommandé)
Utilisez l'exemple dans `contact-form-example.html`

## 🛠️ Personnalisation

### Champs du formulaire supportés :
- `name` (requis)
- `email` (requis)
- `message` (requis)
- `phone` (optionnel)
- `subject` (optionnel)

### Modifier l'email de destination

Dans `sendmail.php`, ligne 28 :
```php
define('TO_EMAIL', '<EMAIL>');
```

### Personnaliser le template email

Modifiez la section HTML dans `sendmail.php` (lignes 85-130)

## 🔍 Test et dépannage

### Test rapide
1. Ouvrez `contact-form-example.html` dans votre navigateur
2. Remplissez le formulaire
3. Vérifiez la réception de l'email

### Problèmes courants

**Erreur "SMTP connect() failed"**
- Vérifiez le mot de passe
- Assurez-vous que l'adresse email existe
- Contactez le support Namecheap

**Erreur 500**
- Vérifiez les permissions des fichiers
- Assurez-vous que PHPMailer est bien uploadé
- Vérifiez les logs d'erreur dans cPanel

**Emails non reçus**
- Vérifiez le dossier spam
- Testez avec une autre adresse email
- Vérifiez les logs du serveur

### Logs d'erreur

Pour activer les logs détaillés, ajoutez dans `sendmail.php` :
```php
$mail->SMTPDebug = 2; // Niveau de debug
$mail->Debugoutput = 'error_log'; // Sortie vers error_log
```

## 📧 Fonctionnalités incluses

✅ **Sécurité** : Protection XSS avec htmlspecialchars()
✅ **Validation** : Email et champs requis
✅ **Responsive** : Compatible mobile
✅ **AJAX** : Envoi sans rechargement
✅ **Template** : Email HTML professionnel
✅ **Namecheap** : Configuration optimisée
✅ **Multilangue** : Prêt pour français/anglais

## 🎨 Intégration avec votre thème

Le formulaire utilise les couleurs Golden Sky Home :
- Or principal : `#9c7719`
- Or secondaire : `#d4af37`
- Arrière-plan sombre : `#1a1a1a`

Adaptez les styles CSS selon vos besoins.

## 📞 Support

En cas de problème :
1. Vérifiez cette documentation
2. Consultez les logs d'erreur cPanel
3. Contactez le support technique Namecheap
4. Testez avec un client email externe (Thunderbird, etc.)

---

**Golden Sky Home - Système d'envoi d'emails**
*Compatible Namecheap - Version 1.0*
