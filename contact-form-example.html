<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire de Contact - Golden Sky Home</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
            color: #f8f8f8;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(26, 26, 26, 0.9);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #9c7719;
            box-shadow: 0 0 30px rgba(156, 119, 25, 0.3);
        }
        h2 {
            color: #9c7719;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #9c7719;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        textarea,
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #9c7719;
            border-radius: 5px;
            background: rgba(26, 26, 26, 0.8);
            color: #f8f8f8;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #d4af37;
            box-shadow: 0 0 10px rgba(156, 119, 25, 0.5);
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(135deg, #9c7719 0%, #d4af37 100%);
            color: #1a1a1a;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: linear-gradient(135deg, #d4af37 0%, #9c7719 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(156, 119, 25, 0.4);
        }
        .required {
            color: #ff6b6b;
        }
        .loading {
            display: none;
            text-align: center;
            color: #9c7719;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Contactez-nous</h2>
        <p style="text-align: center; margin-bottom: 30px;">
            Appart Hôtel Golden Sky Home - Café & Restaurant<br>
            <em>Ouverture le 15 août 2025</em>
        </p>

        <form id="contactForm" action="sendmail.php" method="POST">
            <div class="form-group">
                <label for="name">Nom complet <span class="required">*</span></label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">Adresse email <span class="required">*</span></label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone">Téléphone</label>
                <input type="tel" id="phone" name="phone" placeholder="+212 XXX-XXXXXX">
            </div>

            <div class="form-group">
                <label for="subject">Sujet</label>
                <select id="subject" name="subject">
                    <option value="Demande de réservation">Demande de réservation</option>
                    <option value="Information générale">Information générale</option>
                    <option value="Événement spécial">Événement spécial</option>
                    <option value="Partenariat">Partenariat</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>

            <div class="form-group">
                <label for="message">Message <span class="required">*</span></label>
                <textarea id="message" name="message" placeholder="Décrivez votre demande..." required></textarea>
            </div>

            <button type="submit" class="btn">Envoyer le message</button>
            
            <div class="loading" id="loading">
                <p>Envoi en cours...</p>
            </div>
            
            <div class="message" id="message-result"></div>
        </form>
    </div>

    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const formData = new FormData(form);
            formData.append('ajax', '1');
            
            const loading = document.getElementById('loading');
            const messageDiv = document.getElementById('message-result');
            const submitBtn = form.querySelector('.btn');
            
            // Afficher le loading
            loading.style.display = 'block';
            messageDiv.style.display = 'none';
            submitBtn.disabled = true;
            submitBtn.textContent = 'Envoi en cours...';
            
            fetch('sendmail.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                messageDiv.style.display = 'block';
                messageDiv.className = 'message ' + (data.success ? 'success' : 'error');
                messageDiv.textContent = data.message;
                
                if (data.success) {
                    form.reset();
                }
                
                submitBtn.disabled = false;
                submitBtn.textContent = 'Envoyer le message';
            })
            .catch(error => {
                loading.style.display = 'none';
                messageDiv.style.display = 'block';
                messageDiv.className = 'message error';
                messageDiv.textContent = 'Erreur de connexion. Veuillez réessayer.';
                
                submitBtn.disabled = false;
                submitBtn.textContent = 'Envoyer le message';
            });
        });
    </script>
</body>
</html>
