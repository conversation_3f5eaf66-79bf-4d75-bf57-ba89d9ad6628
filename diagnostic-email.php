<?php
/**
 * Diagnostic complet du système d'envoi d'emails
 * Golden Sky Home - Namecheap
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Diagnostic Email - Golden Sky Home</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #f8f8f8; padding: 20px; margin: 0; }
        .container { max-width: 800px; margin: 0 auto; background: rgba(26, 26, 26, 0.9); padding: 30px; border-radius: 15px; border: 2px solid #9c7719; }
        h1, h2 { color: #9c7719; }
        .success { background: rgba(40, 167, 69, 0.2); border: 1px solid #28a745; color: #28a745; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: rgba(220, 53, 69, 0.2); border: 1px solid #dc3545; color: #dc3545; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background: rgba(255, 193, 7, 0.2); border: 1px solid #ffc107; color: #ffc107; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: rgba(23, 162, 184, 0.2); border: 1px solid #17a2b8; color: #17a2b8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #333; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #9c7719; background: rgba(156, 119, 25, 0.1); }
    </style>
</head>
<body>
<div class='container'>
<h1>🔍 Diagnostic du système d'envoi d'emails</h1>";

// Étape 1: Vérification des fichiers PHPMailer
echo "<div class='step'><h2>📁 Étape 1: Vérification des fichiers PHPMailer</h2>";

$phpmailer_files = [
    'phpmailer/PHPMailer.php',
    'phpmailer/SMTP.php', 
    'phpmailer/Exception.php'
];

$files_ok = true;
foreach ($phpmailer_files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $file trouvé</div>";
    } else {
        echo "<div class='error'>❌ $file manquant</div>";
        $files_ok = false;
    }
}

if (!$files_ok) {
    echo "<div class='error'><strong>ERREUR:</strong> Fichiers PHPMailer manquants. Vérifiez que le dossier phpmailer/ contient tous les fichiers nécessaires.</div>";
    echo "</div></div></body></html>";
    exit;
}
echo "</div>";

// Étape 2: Test d'inclusion PHPMailer
echo "<div class='step'><h2>📦 Étape 2: Test d'inclusion PHPMailer</h2>";

try {
    require_once 'phpmailer/PHPMailer.php';
    require_once 'phpmailer/SMTP.php';
    require_once 'phpmailer/Exception.php';
    echo "<div class='success'>✅ PHPMailer inclus avec succès</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur d'inclusion PHPMailer: " . $e->getMessage() . "</div>";
    echo "</div></div></body></html>";
    exit;
}
echo "</div>";

// Étape 3: Vérification de la configuration
echo "<div class='step'><h2>⚙️ Étape 3: Configuration SMTP</h2>";

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

$config = [
    'SMTP_HOST' => 'goldenskyhome.ma',
    'SMTP_PORT' => 465,
    'SMTP_USERNAME' => '<EMAIL>',
    'SMTP_PASSWORD' => 'Golden2024@@',
    'FROM_EMAIL' => '<EMAIL>',
    'TO_EMAIL' => '<EMAIL>'
];

foreach ($config as $key => $value) {
    if ($key === 'SMTP_PASSWORD') {
        $display_value = str_repeat('*', strlen($value));
    } else {
        $display_value = $value;
    }
    echo "<div class='info'><strong>$key:</strong> $display_value</div>";
}
echo "</div>";

// Étape 4: Test de connexion SMTP
echo "<div class='step'><h2>🔌 Étape 4: Test de connexion SMTP</h2>";

try {
    $mail = new PHPMailer(true);
    
    // Configuration SMTP avec debug
    $mail->isSMTP();
    $mail->Host = $config['SMTP_HOST'];
    $mail->SMTPAuth = true;
    $mail->Username = $config['SMTP_USERNAME'];
    $mail->Password = $config['SMTP_PASSWORD'];
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = $config['SMTP_PORT'];
    
    // Options SSL pour Namecheap
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );
    
    // Activer le debug
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = function($str, $level) {
        echo "<pre style='margin: 5px 0; font-size: 12px;'>$str</pre>";
    };
    
    echo "<div class='info'>Tentative de connexion SMTP...</div>";
    
    // Test de connexion
    if ($mail->smtpConnect()) {
        echo "<div class='success'>✅ Connexion SMTP réussie !</div>";
        $mail->smtpClose();
    } else {
        echo "<div class='error'>❌ Échec de la connexion SMTP</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur de connexion SMTP: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Étape 5: Test d'envoi complet
echo "<div class='step'><h2>📧 Étape 5: Test d'envoi d'email</h2>";

try {
    $mail = new PHPMailer(true);
    
    // Configuration SMTP
    $mail->isSMTP();
    $mail->Host = $config['SMTP_HOST'];
    $mail->SMTPAuth = true;
    $mail->Username = $config['SMTP_USERNAME'];
    $mail->Password = $config['SMTP_PASSWORD'];
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = $config['SMTP_PORT'];
    
    // Options SSL pour Namecheap
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );
    
    // Configuration de l'email
    $mail->setFrom($config['FROM_EMAIL'], 'Golden Sky Home - Test');
    $mail->addAddress($config['TO_EMAIL']);
    $mail->isHTML(true);
    $mail->CharSet = 'UTF-8';
    $mail->Subject = 'Test diagnostic - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <h2>Test de diagnostic réussi !</h2>
    <p>Ce message confirme que le système d\'envoi d\'emails fonctionne correctement.</p>
    <p><strong>Date du test:</strong> ' . date('d/m/Y à H:i:s') . '</p>
    <p><strong>Configuration:</strong> Namecheap SMTP SSL</p>
    <hr>
    <p><em>Golden Sky Home - Système d\'emails</em></p>
    ';
    
    // Tentative d'envoi
    if ($mail->send()) {
        echo "<div class='success'>✅ Email envoyé avec succès !</div>";
        echo "<div class='info'>Vérifiez votre boîte de réception: {$config['TO_EMAIL']}</div>";
    } else {
        echo "<div class='error'>❌ Échec de l'envoi d'email</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur d'envoi: " . $e->getMessage() . "</div>";
    echo "<div class='warning'><strong>Détails de l'erreur:</strong><br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
    echo "Message: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Étape 6: Informations système
echo "<div class='step'><h2>💻 Étape 6: Informations système</h2>";

echo "<div class='info'>";
echo "<strong>Version PHP:</strong> " . phpversion() . "<br>";
echo "<strong>Extensions chargées:</strong><br>";

$required_extensions = ['openssl', 'sockets', 'curl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext<br>";
    } else {
        echo "❌ $ext (manquant)<br>";
    }
}

echo "<strong>Timezone:</strong> " . date_default_timezone_get() . "<br>";
echo "<strong>Date/Heure serveur:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "</div>";
echo "</div>";

// Recommandations
echo "<div class='step'><h2>💡 Recommandations</h2>";

echo "<div class='warning'>";
echo "<h3>Si vous voyez des erreurs :</h3>";
echo "<ul>";
echo "<li><strong>Erreur de connexion SMTP:</strong> Vérifiez le mot de passe dans Namecheap cPanel</li>";
echo "<li><strong>Erreur SSL:</strong> Contactez le support Namecheap pour vérifier la configuration SSL</li>";
echo "<li><strong>Erreur d'authentification:</strong> Vérifiez que l'adresse email existe et est active</li>";
echo "<li><strong>Timeout:</strong> Essayez le port 587 avec STARTTLS au lieu de 465</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>Configuration alternative (si 465 ne fonctionne pas) :</h3>";
echo "<pre>";
echo "Port: 587\n";
echo "Sécurité: STARTTLS\n";
echo "Modifiez dans sendmail.php :\n";
echo "define('SMTP_PORT', 587);\n";
echo "\$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;";
echo "</pre>";
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>🔒 Sécurité</h2>";
echo "<div class='error'><strong>IMPORTANT:</strong> Supprimez ce fichier diagnostic-email.php après utilisation !</div>";
echo "</div>";

echo "</div></body></html>";
?>
