<?php
/**
 * Sendmail Script for Golden Sky Home
 * Compatible with Namecheap shared hosting
 * Uses PHPMailer for SMTP email sending
 */

// Prevent direct access
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    exit('Méthode non autorisée');
}

// Include PHPMailer files
require_once 'phpmailer/PHPMailer.php';
require_once 'phpmailer/SMTP.php';
require_once 'phpmailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Configuration SMTP pour Namecheap
define('SMTP_HOST', 'goldenskyhome.ma');
define('SMTP_PORT', 465);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'Golden2024@@'); // SAISISSEZ ICI LE MOT DE <NAME_EMAIL>
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Golden Sky Home - Réservations');
define('TO_EMAIL', '<EMAIL>');

// Fonction pour nettoyer les données d'entrée
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Fonction pour valider l'email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Initialiser les variables
$response = array('success' => false, 'message' => '');

try {
    // Vérifier que les champs requis sont présents
    if (!isset($_POST['name']) || !isset($_POST['email']) || !isset($_POST['message'])) {
        throw new Exception('Tous les champs sont requis.');
    }

    // Nettoyer et valider les données
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $message = sanitizeInput($_POST['message']);
    $phone = isset($_POST['phone']) ? sanitizeInput($_POST['phone']) : '';
    $subject = isset($_POST['subject']) ? sanitizeInput($_POST['subject']) : 'Nouvelle demande de réservation';

    // Validation
    if (empty($name) || strlen($name) < 2) {
        throw new Exception('Le nom doit contenir au moins 2 caractères.');
    }

    if (!validateEmail($email)) {
        throw new Exception('Adresse email invalide.');
    }

    if (empty($message) || strlen($message) < 10) {
        throw new Exception('Le message doit contenir au moins 10 caractères.');
    }

    // Créer une instance PHPMailer
    $mail = new PHPMailer(true);

    // Configuration SMTP
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
    $mail->Port = SMTP_PORT;

    // Configuration pour Namecheap (important)
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );

    // Paramètres de l'email
    $mail->setFrom(FROM_EMAIL, FROM_NAME);
    $mail->addAddress(TO_EMAIL);
    $mail->addReplyTo($email, $name);

    // Contenu de l'email
    $mail->isHTML(true);
    $mail->CharSet = 'UTF-8';
    $mail->Subject = $subject . ' - ' . $name;

    // Corps de l'email en HTML
    $htmlBody = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #9c7719, #d4af37); color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #9c7719; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Nouvelle demande de réservation</h2>
                <p>Golden Sky Home - Café & Restaurant</p>
            </div>
            <div class='content'>
                <div class='field'>
                    <span class='label'>Nom :</span> {$name}
                </div>
                <div class='field'>
                    <span class='label'>Email :</span> {$email}
                </div>";
    
    if (!empty($phone)) {
        $htmlBody .= "
                <div class='field'>
                    <span class='label'>Téléphone :</span> {$phone}
                </div>";
    }
    
    $htmlBody .= "
                <div class='field'>
                    <span class='label'>Message :</span><br>
                    " . nl2br($message) . "
                </div>
                <div class='field'>
                    <span class='label'>Date de réception :</span> " . date('d/m/Y à H:i:s') . "
                </div>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé depuis le site web Golden Sky Home</p>
                <p>Béni Mellal, Maroc</p>
            </div>
        </div>
    </body>
    </html>";

    $mail->Body = $htmlBody;

    // Version texte alternative
    $textBody = "Nouvelle demande de réservation\n\n";
    $textBody .= "Nom: {$name}\n";
    $textBody .= "Email: {$email}\n";
    if (!empty($phone)) {
        $textBody .= "Téléphone: {$phone}\n";
    }
    $textBody .= "Message:\n{$message}\n\n";
    $textBody .= "Date: " . date('d/m/Y à H:i:s') . "\n";
    $textBody .= "\nGolden Sky Home - Béni Mellal, Maroc";

    $mail->AltBody = $textBody;

    // Envoyer l'email
    $mail->send();

    $response['success'] = true;
    $response['message'] = 'Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.';

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Erreur lors de l\'envoi : ' . $e->getMessage();
    
    // Log l'erreur (optionnel)
    error_log('Sendmail Error: ' . $e->getMessage());
}

// Retourner la réponse
if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
    // Réponse AJAX
    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    // Réponse HTML standard
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Résultat de l'envoi - Golden Sky Home</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .success { color: #28a745; border: 2px solid #28a745; background: #d4edda; padding: 15px; border-radius: 5px; }
            .error { color: #dc3545; border: 2px solid #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; }
            .btn { display: inline-block; padding: 10px 20px; background: #9c7719; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
            .btn:hover { background: #7a5c14; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Golden Sky Home</h2>
            <div class="<?php echo $response['success'] ? 'success' : 'error'; ?>">
                <?php echo $response['message']; ?>
            </div>
            <a href="javascript:history.back()" class="btn">Retour</a>
        </div>
    </body>
    </html>
    <?php
}
?>
